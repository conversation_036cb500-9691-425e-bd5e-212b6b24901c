import { buttonVariants } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Image from "next/image";
import Link from "next/link";


export default function AuthLayout({ children }: { children: React.ReactNode }) {
    return (
        <div className="relative flex min-h-svh flex-col items-center justify-center">
            <Link href="/" className={buttonVariants({
                variant: "outline",
                className: "absolute top-4 left-4",
            })}>
                <ArrowLeft className="size-4" />
                Back
            </Link>
            <div className="flex w-full max-w-sm flex-col gap-6">
                <Link href="/" className="flex items-center gap-2 self-center font-medium">
                    <Image src="/vercel.svg" alt="Logo" width={30} height={30} />
                    Excel Insights
                </Link>
                {children}
                <div className="text-balance text-center text-xs text-muted-foreground">
                    By clicking continue, you agree to our <span className="cursor-pointer hover:underline hover:text-primary">Terms of service</span>
                    {" "}
                    and <span className="cursor-pointer hover:underline hover:text-primary">Privacy Policy</span>.
                </div>
            </div>

        </div>
    );
}