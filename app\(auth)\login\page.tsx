"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { authClient } from "@/lib/auth-client";
import { GithubIcon, Loader } from "lucide-react";
import { useTransition } from "react";
import { toast } from "sonner";

export default function LoginPage() {
    const [githubPending, startGithubTransition] = useTransition();
    async function signInWithGithub() {
        startGithubTransition(async () => {
            await authClient.signIn.social({
            provider: "github",
            callbackURL: "/",
            fetchOptions: {
                onSuccess: () => {
                    toast.success("Logged in successfully!");
                },
                onError: () => {
                    toast.error('Internal server error');
                },
            },
        });
        });
    };
    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-xl">Welcome Back</CardTitle>
                <CardDescription>
                    Login with your github account
                </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col gap-3">
                <Button disabled={githubPending} onClick={signInWithGithub} className="w-full cursor-pointer" variant={"outline"}>
                    {githubPending ? (
                        <>
                        <Loader className="size-4 animate-spin"/>
                        <span>Loading...</span>
                        </>
                    ) : (
                        <>
                        <GithubIcon className="size-4" />
                        Login with Github</>
                    )}
                </Button>
                <div className="relative text-center text-sm after:absolute
                    after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center
                    after:border-t after:border-border">
                    <span className="relative z-10 bg-card px-2 text-muted-foreground">Or continue with</span>
                </div>
                <div className="grid gap-3">
                    <div className="grid gap-2">
                        <Label htmlFor="email">Email</Label>
                        <Input type="email"  placeholder="<EMAIL>"/>
                    </div>
                    <Button className="w-full">Continue with email</Button>
                </div>
            </CardContent>
        </Card>
    );
}